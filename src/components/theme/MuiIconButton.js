import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiIconButtonTheme = (currentPrimaryColor, themeName) => ({
  MuiIconButton: {
    styleOverrides: {
      root: {
        height: '40px',
        width: '40px',
        padding: '0px',
        borderRadius: '4px',
        color: styles.darkColor300,
        '&:hover': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
          color: getBrandColors(currentPrimaryColor, themeName)[500],
          '& path': {
            stroke: getBrandColors(currentPrimaryColor, themeName)[500],
          },
        },
        '&.active': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
          color: getBrandColors(currentPrimaryColor, themeName)[500],
          '& path': {
            stroke: getBrandColors(currentPrimaryColor, themeName)[500],
          },
        },
        '&.primary-light': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
          color: getBrandColors(currentPrimaryColor, themeName)[500],
          '& svg path': {
            stroke: getBrandColors(currentPrimaryColor, themeName)[500],
          },
          '&:hover': {
            backgroundColor: getBrandColors(currentPrimaryColor, themeName)[100],
          },
        },
      },
    },
  },
});

export default MuiIconButtonTheme;
