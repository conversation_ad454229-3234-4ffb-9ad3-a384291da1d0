import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiAutocompleteTheme = (currentPrimaryColor, themeName) => ({
  MuiAutocomplete: {
    styleOverrides: {
      root: {
        '& .MuiInputLabel-root': {
          transform: 'translate(16px, 12px) scale(1)',
        },
      },
      option: {
        color: `${styles.darkColor500} !important`,
        height: '40px',
        '&[aria-selected="true"]': {
          backgroundColor: styles.lightColor100,
        },
        '&[data-focus="true"]': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        },
        '&:active': {
          backgroundColor: styles.lightColor100,
        },
        '& .MuiCheckbox-root': {
          marginLeft: '-12px',
          '& .MuiIconButton-label': {
            height: '22px',
          },
        },
      },
      inputRoot: {
        '&[class*="MuiOutlinedInput-root"]': {
          padding: 0,
          '& .MuiAutocomplete-input:first-child': {
            paddingLeft: '12px',
          },
        },
        '& .MuiInputBase-input': {
          marginLeft: '8px',
        },
      },
      popper: {
        filter: `drop-shadow(${styles.popupShadow})`,
      },
      paper: {
        margin: 0,
      },
      listbox: {
        padding: 0,
      },
      endAdornment: {
        top: 'calc(50% - 10px)',
      },
      tag: {
        height: '24px',
        borderRadius: '4px',
        backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        color: `${styles.darkColor500} !important`,
        display: 'inline-flex',
        alignItems: 'center',
        padding: '0 6px',
        margin: '2px 0px 2px 8px',
        fontSize: '12px',
        '& .MuiChip-label': {
          padding: '0',
        },
        '& .MuiChip-deleteIcon': {
          color: styles.darkColor300,
          margin: '0 0 0 7px',
          width: '16px',
        },
      },
    },
  },
});

export default MuiAutocompleteTheme;
