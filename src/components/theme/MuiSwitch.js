import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiSwitchTheme = (primaryColor, themeName) => ({
  MuiSwitch: {
    styleOverrides: {
      root: {
        '& .MuiIconButton-root:hover': {
          backgroundColor: 'unset',
        },
      },
      track: {
        backgroundColor: styles.lightColor300,
        opacity: '1!important',
      },
      thumb: {
        color: 'white',
        boxShadow: '0px 0px 2px rgba(0, 0, 0, 0.12), 0px 2px 2px rgba(0, 0, 0, 0.24)',
      },
      colorPrimary: {
        '&.Mui-checked:hover': {
          backgroundColor: 'unset!important',
        },
        '&.Mui-checked + .MuiSwitch-track': {
          backgroundColor: getBrandColors(primaryColor, themeName)[100],
        },
        '&.Mui-checked .MuiSwitch-thumb': {
          color: getBrandColors(primaryColor, themeName)[500],
        },
      },
    },
  },
});

export default MuiSwitchTheme;
