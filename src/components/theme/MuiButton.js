import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiButtonTheme = (currentPrimaryColor, themeName) => ({
  MuiButton: {
    styleOverrides: {
      root: {
        minHeight: '40px',
        fontSize: '12px',
        fontWeight: '700',
        letterSpacing: '0.4px',
      },
      contained: {
        boxShadow: 'none',
        '&:hover': {
          boxShadow: 'none',
        },
      },
      containedPrimary: {
        '&:hover': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[400],
        },
        '&.Mui-disabled': {
          backgroundColor: styles.lightColor300,
          color: styles.lightColor50,
        },
      },
      textPrimary: {
        color: styles.darkColor500,
        '&:hover': {
          color: `${getBrandColors(currentPrimaryColor, themeName)[500]}!important`,
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
          '& path': {
            fill: getBrandColors(currentPrimaryColor, themeName)[500],
          },
        },
      },
      outlinedPrimary: {
        borderColor: getBrandColors(currentPrimaryColor, themeName)[500],
        '&:hover': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        },
      },
      iconSizeLarge: {
        '& > *:first-child': {
          fontSize: '24px',
        },
      },
      containedSecondary: {
        color: `${getBrandColors(currentPrimaryColor, themeName)[500]}!important`,
        backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        '&:hover': {
          color: `${getBrandColors(currentPrimaryColor, themeName)[400]}!important`,
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[100],
        },
      },
    },
  },
});

export default MuiButtonTheme;
