import themeConfig from '../configs/themeConfig';

const setFavicon = (themeName) => {
  const faviconLight = document.createElement('link');
  const faviconDark = document.createElement('link');
  const relName = 'icon';

  faviconLight.rel = relName;
  faviconLight.media = '(prefers-color-scheme: light)';
  faviconLight.href = themeConfig[themeName].faviconLight;
  faviconDark.rel = relName;
  faviconDark.media = '(prefers-color-scheme: dark)';
  faviconDark.href = themeConfig[themeName].faviconDark;

  document.querySelector(`link[rel*=${relName}]`).remove();
  document.head.appendChild(faviconLight);
  document.head.appendChild(faviconDark);
};

export default setFavicon;
