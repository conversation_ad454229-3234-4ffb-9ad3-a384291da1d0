import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiListTheme = (currentPrimaryColor, themeName) => ({
  MuiList: {
    styleOverrides: {
      padding: {
        paddingTop: '0',
        paddingBottom: '0',
      },
    },
  },
  MuiListItem: {
    styleOverrides: {
      root: {
        color: styles.darkColor500,
        '&.Mui-selected': {
          backgroundColor: styles.lightColor100,
        },
        '&.Mui-selected:hover': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        },
      },
      button: {
        '&:hover': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        },
      },
    },
  },
});

export default MuiListTheme;
