import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiChipTheme = (primaryColor, themeName) => ({
  MuiChip: {
    styleOverrides: {
      root: {
        borderRadius: 4,
        fontSize: 12,
        lineHeight: '140%',
      },
      colorPrimary: {
        backgroundColor: getBrandColors(primaryColor, themeName)[50],
        color: styles.darkColor500,
      },
    },
  },
});

export default MuiChipTheme;
