import getDefaultBrandColors from './getDefaultBrandColors';
import getBTBrandColors from './getBTBrandColors';
import getBoltBrandColors from './getBoltBrandColors';
import getOrangeBrandColor from './getOrangeBrandColor';
import getSimplifyBrandColor from './getSimplifyBrandColor';
import getTelstra from './getTelstra';

const brandColorsConfig = {
  default: (color) => getDefaultBrandColors(color),
  bt: (color) => getBTBrandColors(color),
  bolt: (color) => getBoltBrandColors(color), // added for bolt
  orange: (color) => getOrangeBrandColor(color), // orange
  simplify: (color) => getSimplifyBrandColor(color), // simplify
  telstra: (color) => getTelstra(color),
};

const getBrandColors = (hex, themeName = 'default') => {
  // Add debugging to help identify issues
  if (!hex) {
    console.warn('getBrandColors: hex parameter is missing or falsy', { hex, themeName });
    return null;
  }

  try {
    const result = Object.keys(brandColorsConfig).includes(themeName)
      ? brandColorsConfig[themeName](hex)
      : brandColorsConfig.default(hex);

    // Validate the result has the expected structure
    if (!result || typeof result !== 'object') {
      console.warn('getBrandColors: Invalid result returned', { hex, themeName, result });
      return null;
    }

    // Check if required color keys exist
    const requiredKeys = ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'];
    const missingKeys = requiredKeys.filter(key => !(key in result));

    if (missingKeys.length > 0) {
      console.warn('getBrandColors: Missing color keys', { hex, themeName, missingKeys, result });
      return null;
    }

    return result;
  } catch (error) {
    console.error('getBrandColors: Error occurred', { hex, themeName, error });
    return null;
  }
};
export default getBrandColors;
