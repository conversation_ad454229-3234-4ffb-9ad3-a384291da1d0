import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiButtonTheme = (currentPrimaryColor, themeName) => {
  // Get brand colors with fallback
  const brandColors = getBrandColors(currentPrimaryColor, themeName);

  // Fallback colors if getBrandColors fails
  const fallbackColors = {
    50: '#f9f9f9',
    100: '#f0f0f0',
    200: '#e0e0e0',
    300: '#c0c0c0',
    400: '#a0a0a0',
    500: currentPrimaryColor || '#808080',
    600: '#606060',
    700: '#404040',
    800: '#202020',
    900: '#101010',
  };

  const colors = brandColors || fallbackColors;

  return {
  MuiButton: {
    styleOverrides: {
      root: {
        minHeight: '40px',
        fontSize: '12px',
        fontWeight: '700',
        letterSpacing: '0.4px',
      },
      contained: {
        boxShadow: 'none',
        '&:hover': {
          boxShadow: 'none',
        },
      },
      containedPrimary: {
        '&:hover': {
          backgroundColor: colors[400],
        },
        '&.Mui-disabled': {
          backgroundColor: styles.lightColor300,
          color: styles.lightColor50,
        },
      },
      textPrimary: {
        color: styles.darkColor500,
        '&:hover': {
          color: `${colors[500]}!important`,
          backgroundColor: colors[50],
          '& path': {
            fill: colors[500],
          },
        },
      },
      outlinedPrimary: {
        borderColor: colors[500],
        '&:hover': {
          backgroundColor: colors[50],
        },
      },
      iconSizeLarge: {
        '& > *:first-child': {
          fontSize: '24px',
        },
      },
      containedSecondary: {
        color: `${colors[500]}!important`,
        backgroundColor: colors[50],
        '&:hover': {
          color: `${colors[400]}!important`,
          backgroundColor: colors[100],
        },
      },
    },
  },
  };
};

export default MuiButtonTheme;
