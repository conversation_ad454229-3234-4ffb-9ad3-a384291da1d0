import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiTableTheme = (currentPrimaryColor, themeName) => ({
  MuiTable: {
    styleOverrides: {
      root: {
        color: styles.darkColor500,
        fontSize: '12px',
        lineHeight: '16px',
        fontWeight: '400',
      },
    },
  },
  MuiTableRow: {
    styleOverrides: {
      root: {
        '&:hover': {
          backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
        },
      },
    },
  },
  MuiTableCell: {
    styleOverrides: {
      head: {
        paddingTop: '14px',
        paddingBottom: '14px',
        fontWeight: '700',
        color: styles.darkColor500,
        backgroundColor: `${styles.lightColor100}!important`,
        borderTopWidth: '1px',
        borderTopStyle: 'solid',
        borderTopColor: styles.borderColor,
      },
      root: {
        paddingLeft: '8px',
        paddingRight: '14px',
        paddingTop: '8px',
        paddingBottom: '8px',
        borderBottomColor: styles.borderColor,
        '&:first-child': {
          borderLeftWidth: '1px',
          borderLeftStyle: 'solid',
          borderLeftColor: styles.borderColor,
        },
        '&:last-child': {
          borderRightWidth: '1px',
          borderRightStyle: 'solid',
          borderRightColor: styles.borderColor,
        },
      },
    },
  },
});

export default MuiTableTheme;
