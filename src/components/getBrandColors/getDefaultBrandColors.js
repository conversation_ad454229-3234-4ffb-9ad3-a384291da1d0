import tinycolor from 'tinycolor2';
import additionalColors from './additionalColors';

const brandColors = ['#2D2A81', '#F3C73C', '#007AC2', '#95C11F'];
const getColor = (value) => value?.toHexString();

const getBrandColors = (hex) => ({
  50: getColor(tinycolor(hex).lighten(58), '50'),
  100: getColor(tinycolor(hex).lighten(52), '100'),
  200: getColor(tinycolor(hex).lighten(40), '200'),
  300: getColor(tinycolor(hex).lighten(28), '300'),
  400: getColor(tinycolor(hex).lighten(11), '400'),
  500: getColor(tinycolor(hex), '500'),
  600: getColor(tinycolor(hex).darken(6), '600'),
  700: getColor(tinycolor(hex).darken(12), '700'),
  800: getColor(tinycolor(hex).darken(18), '800'),
  900: getColor(tinycolor(hex).darken(24), '900'),
});

const getDefaultBrandColors = (hex) => {
  const isBrandColor = brandColors.includes(hex?.toUpperCase());

  return isBrandColor ? getBrandColors(hex) : additionalColors[hex?.toUpperCase()];
};

export default getDefaultBrandColors;
