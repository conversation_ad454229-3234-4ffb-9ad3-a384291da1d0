default:
  image: node:latest
  before_script:
    - npm ci --cache .npm --prefer-offline
    - |
      {
        echo "@${CI_PROJECT_ROOT_NAMESPACE}:registry=${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/npm/"
        echo "${CI_API_V4_URL#https?}/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=\${CI_JOB_TOKEN}"
      } | tee --append .npmrc
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .npm/

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH

variables:
  NPM_TOKEN: ${CI_JOB_TOKEN}

stages:
  - release

publish:
  stage: release
  script:
    - npm run semantic-release
    - rm -rf .npmrc
    - apt update && apt-get install expect -y
    - |
      expect <<EOD
      spawn npm login --registry=https://nexus.ops.connectedplatform.net/repository/npm-hosted/
      expect "Username:"
      send "$NEXUS_USERNAME\r"
      expect "Password:"
      send "$NEXUS_PASSWORD\r"
      expect eof
      EOD
    - npm publish --registry=https://nexus.ops.connectedplatform.net/repository/npm-hosted/    
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
