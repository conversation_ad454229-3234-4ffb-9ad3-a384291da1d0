{"name": "@nv2/nv2-pkg-js-theme", "version": "2.11.3", "main": "src/components/index.js", "files": ["src/components"], "repository": {"type": "git", "url": "****************************:nv2/pkg/js/nv2-pkg-js-theme.git"}, "dependencies": {"@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@mui/lab": "^5.0.0-alpha.95", "@mui/material": "^5.10.1", "@mui/styles": "^5.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "sass": "^1.55.0", "tinycolor2": "^1.4.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "lint": "eslint --ext .js --ext .jsx src", "test": "react-scripts test", "eject": "react-scripts eject", "semantic-release": "semantic-release"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "publishConfig": {"access": "public"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.18.10", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^9.4.2", "@semantic-release/npm": "^9.0.1", "eslint": "^8.24.0", "eslint-config-airbnb": "^19.0.4", "semantic-release": "^19.0.5"}}