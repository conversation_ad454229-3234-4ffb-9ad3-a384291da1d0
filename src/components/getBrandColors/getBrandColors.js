import getDefaultBrandColors from './getDefaultBrandColors';
import getBTBrandColors from './getBTBrandColors';
import getBoltBrandColors from './getBoltBrandColors';
import getOrangeBrandColor from './getOrangeBrandColor';
import getSimplifyBrandColor from './getSimplifyBrandColor';
import getTelstra from './getTelstra';

const brandColorsConfig = {
  default: (color) => getDefaultBrandColors(color),
  bt: (color) => getBTBrandColors(color),
  bolt: (color) => getBoltBrandColors(color), // added for bolt
  orange: (color) => getOrangeBrandColor(color), // orange
  simplify: (color) => getSimplifyBrandColor(color), // simplify
  telstra: (color) => getTelstra(color),
};

const getBrandColors = (hex, themeName = 'default') => (
  Object.keys(brandColorsConfig).includes(themeName)
    ? brandColorsConfig[themeName](hex)
    : brandColorsConfig.default(hex)
);
export default getBrandColors;
