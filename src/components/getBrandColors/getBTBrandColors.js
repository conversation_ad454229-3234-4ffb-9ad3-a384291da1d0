import tinycolor from 'tinycolor2';

const getColor = (value) => value.toHexString();

const getMixedColor = (rgb1, rgb2) => {
  const r = Math.floor((rgb1.r * rgb2.r) / 255);
  const g = Math.floor((rgb1.g * rgb2.g) / 255);
  const b = Math.floor((rgb1.b * rgb2.b) / 255);

  return tinycolor(`rgb ${r} ${g} ${b}`);
};

const getBTBrandColors = (hex) => {
  const baseLight = tinycolor('#ffffff');
  const baseDark = getMixedColor(
    tinycolor(hex).toRgb(),
    tinycolor(hex).toRgb(),
  );

  return {
    50: getColor(tinycolor.mix(baseLight, hex, 12), '50'),
    100: getColor(tinycolor.mix(baseLight, hex, 30), '100'),
    200: getColor(tinycolor.mix(baseLight, hex, 50), '200'),
    300: getColor(tinycolor.mix(baseLight, hex, 70), '300'),
    400: getColor(tinycolor.mix(baseLight, hex, 85), '400'),
    500: getColor(tinycolor.mix(baseLight, hex, 100), '500'),
    600: getColor(tinycolor.mix(baseDark, hex, 87), '600'),
    700: getColor(tinycolor.mix(baseDark, hex, 70), '700'),
    800: getColor(tinycolor.mix(baseDark, hex, 54), '800'),
    900: getColor(tinycolor.mix(baseDark, hex, 25), '900'),
  };
};

export default getBTBrandColors;
