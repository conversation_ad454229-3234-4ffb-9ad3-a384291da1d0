import React from 'react';

import { But<PERSON>, Checkbox } from '@mui/material';
import { ThemeProvider } from '@mui/styles';

import theme from 'components/theme/theme';
import themeConfig from 'components/configs/themeConfig';

function App() {
  return (
    <ThemeProvider theme={theme(themeConfig.nextgen)}>
      <Button id="submit" type="submit" variant="contained" color="primary">
        Primary outlined button
      </Button>
      <Checkbox
        checked="true"
        name="remember"
        color="secondary"
        icon={<span className="MuiCheckbox-icon" />}
      />
    </ThemeProvider>
  );
}

export default App;
