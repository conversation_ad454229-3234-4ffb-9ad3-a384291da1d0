// eslint-disable-next-line camelcase
import { createTheme, unstable_createMuiStrictModeTheme } from '@mui/material/styles';
import MuiButtonTheme from './MuiButton';
import MuiOutlinedInputTheme from './MuiOutlinedInput';
import MuiCheckboxTheme from './MuiCheckbox';
import MuiTypographyTheme from './MuiTypography';
import MuiPaperTheme from './MuiPaper';
import MuiLinkTheme from './MuiLink';
import MuiSwitchTheme from './MuiSwitch';
import MuiTableTheme from './MuiTable';
import MuiTooltipTheme from './MuiTooltip';
import MuiSelectTheme from './MuiSelect';
import MuiListTheme from './MuiList';
import MuiAutocompleteTheme from './MuiAutocomplete';
import MuiIconButtonTheme from './MuiIconButton';
import MuiChipTheme from './MuiChipTheme';
import MuiFormGroupTheme from './MuiFormGroup';
import MuiTabTheme from './MuiTab';
import defaultTypographyConfig from '../configs/defaultTypographyConfig';

const isDevelopment = process.env.NODE_ENV === 'development';
// eslint-disable-next-line camelcase
const createCustomTheme = isDevelopment ? unstable_createMuiStrictModeTheme : createTheme;

const theme = (themeConfig) => createCustomTheme({
  palette: {
    primary: {
      main: themeConfig.primaryColor,
    },
    secondary: {
      main: themeConfig.secondaryColor,
    },
  },
  typography: {
    ...defaultTypographyConfig,
    ...themeConfig.typographyConfig,
  },
  spacing: 4,
  components: {
    ...MuiButtonTheme(themeConfig?.primaryColor, themeConfig?.themeName),
    ...MuiOutlinedInputTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiCheckboxTheme,
    ...MuiTypographyTheme,
    ...MuiPaperTheme,
    ...MuiLinkTheme(themeConfig.primaryColor),
    ...MuiSwitchTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiTableTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiTooltipTheme,
    ...MuiSelectTheme,
    ...MuiListTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiAutocompleteTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiIconButtonTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiChipTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiFormGroupTheme(themeConfig.primaryColor, themeConfig.themeName),
    ...MuiTabTheme,
  },
});

export default theme;
