// Test script to verify theme functionality
const getBrandColors = require('./src/components/getBrandColors/getBrandColors.js').default;
const MuiButtonTheme = require('./src/components/theme/MuiButton.js').default;

console.log('Testing getBrandColors function...');

// Test with valid color and theme
console.log('Test 1: Valid color and default theme');
const result1 = getBrandColors('#2D2A81', 'default');
console.log('Result:', result1);

// Test with invalid color
console.log('\nTest 2: Invalid color');
const result2 = getBrandColors(null, 'default');
console.log('Result:', result2);

// Test with unknown theme
console.log('\nTest 3: Unknown theme');
const result3 = getBrandColors('#2D2A81', 'unknown');
console.log('Result:', result3);

// Test MuiButtonTheme
console.log('\nTesting MuiButtonTheme...');
const buttonTheme = MuiButtonTheme('#2D2A81', 'default');
console.log('Button theme created successfully:', !!buttonTheme.MuiButton);

console.log('\nAll tests completed!');
