import styles from '../styles/variables.module.scss';

const mediaMinWidthSmallMobile = `@media (min-width: ${styles.smallMobileWidth})`;

const MuiTooltipTheme = {
  MuiTooltip: {
    styleOverrides: {
      tooltip: {
        fontSize: '12px',
        lineHeight: '140%',
        paddingTop: '8px',
        paddingRight: '12px',
        paddingBottom: '8px',
        paddingLeft: '12px',
        backgroundColor: styles.darkColor300,
      },
      arrow: {
        color: styles.darkColor300,
      },
      tooltipPlacementTop: {
        marginTop: '16px',
        [mediaMinWidthSmallMobile]: {
          marginTop: '16px',
        },
      },
    },
  },
};

export default MuiTooltipTheme;
