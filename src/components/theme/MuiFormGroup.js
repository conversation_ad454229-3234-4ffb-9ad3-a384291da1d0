import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiFormGroupTheme = (currentPrimaryColor, themeName) => ({
  MuiFormGroup: {
    styleOverrides: {
      root: {
        '&.radiogroup-tabs': {
          '& .MuiRadio-root': {
            display: 'none',
            '&.Mui-checked': {
              '& + .MuiFormControlLabel-label': {
                backgroundColor: getBrandColors(currentPrimaryColor, themeName)[50],
                borderLeftColor: currentPrimaryColor,
              },
            },
          },
          '& .MuiFormControlLabel-root': {
            margin: '0',
          },
          '& .MuiFormControlLabel-label': {
            width: '100%',
            height: '50px',
            display: 'flex',
            padding: '0 20px',
            alignItems: 'center',
            borderLeft: '4px solid white',
            transition: '0.2s',
            '&:hover': {
              backgroundColor: styles.lightColor100,
              borderLeftColor: styles.lightColor200,
            },
          },
        },
      },
    },
  },
});

export default MuiFormGroupTheme;
