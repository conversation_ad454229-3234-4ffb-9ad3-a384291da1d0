import nextgenLogo from '../images/ts-nextgen-header-logo.svg';
import boltLogo from '../images/bolt-logo.svg';
import TNSLogo from '../images/tns-logo.svg';
import TNSLogoMob from '../images/ts-nextgen-header-logo-mob.svg';
import BTLogo from '../images/bt-logo.svg';
import SIMPLIFYLogo from '../images/simplify-logo.svg';
import TELSTRALogo from '../images/telstra.svg';
import faviconRakutenLight from '../images/rakuten-logo.svg';
import faviconLight from '../images/favicon-light.ico';
import faviconDark from '../images/favicon-dark.ico';
import faviconBTLight from '../images/favicon-bt-light.ico';
import faviconBTDark from '../images/favicon-bt-dark.ico';
import faviconboltLight from '../images/bolt-favicon.ico';

const defaultThemeColors = {
  darkColor: '#1C1C28',
  lightColor: '#BCBDCD',
  greenColor: '#359B70',
  redColor: '#DA5454',
  blueColor: '#004FC4',
  orangeColor: '#F28100',
  tealColor: '#00B7C4',
  purpleColor: '#4C0099',
  coralColor: '#FF9EB7',
  yellowColor: '#FFDF00',
  landingGradient: 'linear-gradient(102.42deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%, #FFFFFF 100%)',
};

const defaultFavicons = {
  faviconLight,
  faviconDark,
};

const themeConfig = {
  nextgen: {
    themeName: 'nextgen',
    primaryColor: '#2D2A81',
    secondaryColor: '#F3C73C',
    logo: nextgenLogo,
    logoMob: nextgenLogo,
    ...defaultThemeColors,
    ...defaultFavicons,
  },
  bt: {
    themeName: 'bt',
    primaryColor: '#5514B4',
    secondaryColor: '#F200F5',
    darkColor: '#333333',
    lightColor: '#BCBDCD',
    greenColor: '#30B281',
    redColor: '#EB5F64',
    blueColor: '#0296D4',
    orangeColor100: '#FEEEEC',
    orangeColor: '#F7735D',
    tealColor: '#00C9E7',
    purpleColor: '#7E2EC6',
    coralColor: '#FF9EB7',
    yellowColor: '#FFDF00',
    ratePlanCardColorDefault: '#E0F9FC',
    automationIcons: '#D200D6',
    ratePlanCardButtons: '#B3EFF8',
    ratePlanCardColor: '#f7f7f9',
    logo: BTLogo,
    logoMob: BTLogo,
    typography: {
      fontFamily: [
        'BTCurve, sans-serif',
      ],
    },
    landingGradient: 'linear-gradient(180deg, rgba(255, 255, 255, 0) 77.08%, #FFFFFF 100%), linear-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%)',
    faviconLight: faviconBTLight,
    faviconDark: faviconBTDark,
    bgLightPrimary: '#F5F1FA',
    bgLightMediumPrimay: '#CCB9E9',
    bgLightIntermediatePrimary: '#FFF5FF',
    bgSlightDarkPrimary: '#EFEAF7',
    actionBg: '#EBE3F6',
    primaryBgGradient: `radial-gradient(
      72.29% 158.45% at 98.36% 100%,
      rgb(132, 178, 216) 0%,
      rgba(132, 178, 216, 0) 100%
    ),
    radial-gradient(
      53.19% 74.23% at 85.58% 89.32%,
      rgb(182, 136, 190) 0%, 
      rgba(143, 136, 190, 0) 100%
    ),
    rgb(85, 20, 180)`,
    btTextColor: '#A56DD7',
  },
  simplify: {
    themeName: 'simplify',
    primaryColor: '#5514B4',
    secondaryColor: '#F200F5',
    darkColor: '#333333',
    lightColor: '#BCBDCD',
    greenColor: '#30B281',
    redColor: '#EB5F64',
    blueColor: '#0296D4',
    orangeColor100: '#FEEEEC',
    orangeColor: '#F7735D',
    tealColor: '#00C9E7',
    purpleColor: '#7E2EC6',
    coralColor: '#FF9EB7',
    yellowColor: '#FFDF00',
    ratePlanCardColorDefault: '#E0F9FC',
    ratePlanCardButtons: '#B3EFF8',
    ratePlanCardColor: '#f7f7f9',
    logo: SIMPLIFYLogo,
    logoMob: SIMPLIFYLogo,
    typography: {
      fontFamily: [
        'BTCurve, sans-serif',
      ],
    },
    landingGradient: 'linear-gradient(180deg, rgba(255, 255, 255, 0) 77.08%, #FFFFFF 100%), linear-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%)',
    faviconLight: faviconBTLight,
    faviconDark: faviconBTDark,
    bgLightPrimary: '#F5F1FA',
    bgLightMediumPrimay: '#CCB9E9',
    bgLightIntermediatePrimary: '#FFF5FF',
    bgSlightDarkPrimary: '#EFEAF7',
    actionBg: '#EBE3F6',
    automationIcons: '#D200D6',
    primaryBgGradient: `radial-gradient(
      72.29% 158.45% at 98.36% 100%,
      rgb(132, 178, 216) 0%,
      rgba(132, 178, 216, 0) 100%
    ),
    radial-gradient(
      53.19% 74.23% at 85.58% 89.32%,
      rgb(182, 136, 190) 0%, 
      rgba(143, 136, 190, 0) 100%
    ),
    rgb(85, 20, 180)`,
    btTextColor: '#A56DD7',
  },
  orange: {
    themeName: 'orange',
    primaryColor: '#D14900',
    secondaryColor: '#FF9E80',
    darkColor: '#1C1C28',
    lightColor: '#FCEFE8',
    greenColor: '#33A474',
    redColor: '#D12D2D',
    blueColor: '#1A73E8',
    orangeColor100: '#FEEEEC',
    orangeColor: '#FF7A00',
    tealColor: '#00BFC5',
    purpleColor: '#9C27B0',
    coralColor: '#FFA6B0',
    yellowColor: '#FFC107',
    ratePlanCardColorDefault: '#FFF4E5',
    ratePlanCardButtons: '#FFE0B2',
    ratePlanCardColor: '#FFFAF2',
    logo: faviconRakutenLight,
    logoMob: faviconRakutenLight,
    typography: {
      fontFamily: [
        'Helvetica, Arial, sans-serif',
      ],
    },
    landingGradient: 'linear-gradient(180deg, #FFFFFF 0%, rgba(255, 247, 230, 0.9) 100%)',
    faviconLight: faviconRakutenLight,
    faviconDark: faviconRakutenLight,
    bgLightPrimary: '#FFF1E0',
    bgLightMediumPrimay: '#FFD9B3',
    bgLightIntermediatePrimary: '#FFF9F0',
    bgSlightDarkPrimary: '#FFE5CC',
    actionBg: '#F5DBC1',
    automationIcons: '#d66700ff',
    primaryBgGradient: `radial-gradient(
    72.29% 158.45% at 98.36% 100%,
    rgb(255, 184, 122) 0%,
    rgba(255, 184, 122, 0) 100%
  ),
  radial-gradient(
    53.19% 74.23% at 85.58% 89.32%,
    rgb(255, 230, 179) 0%, 
    rgba(255, 230, 179, 0) 100%
  ),
  rgb(255, 140, 0)`,
    btTextColor: '#D14900',
  },
  telstra: {
    themeName: 'telstra',
    primaryColor: '#0d54ff',
    secondaryColor: '#0e54ff',
    darkColor: '#333333',
    lightColor: '#BCBDCD',
    greenColor: '#008a00',
    redColor: '#f96449',
    blueColor: '#0d54ff',
    orangeColor100: '#FEEEEC',
    orangeColor: '#ec7201',
    tealColor: '#6ccbfe',
    purpleColor: '#570066',
    coralColor: '#FF9EB7',
    yellowColor: '#FFDF00',
    ratePlanCardColorDefault: '#ebf1ff',
    ratePlanCardButtons: '#d8f0fe',
    ratePlanCardColor: '#f5ede2',
    logo: TELSTRALogo,
    logoMob: TELSTRALogo,
    typography: {
      fontFamily: [
        'Telstra Akkurat, sans-serif',
      ],
    },
    landingGradient: 'linear-gradient(180deg, rgba(255, 255, 255, 0) 77.08%, #FFFFFF 100%), u-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%)',
    faviconLight: TELSTRALogo,
    faviconDark: TELSTRALogo,
    bgLightPrimary: '#e7ecfb',
    bgLightMediumPrimay: '#b6ccff',
    bgLightIntermediatePrimary: '#e5ecff',
    bgSlightDarkPrimary: '#e2eaff',
    actionBg: '#EBF1FF',
    automationIcons: '#3967d1',
    primaryBgGradient: `radial-gradient(
      72.29% 158.45% at 98.36% 100%,
      rgb(132, 178, 216) 0%,
      rgba(102, 152, 193, 0) 100%
    ),
    radial-gradient(
      53.19% 74.23% at 85.58% 89.32%,
      rgba(132, 161, 197, 1) 0%,
      rgba(143, 136, 190, 0) 100%
    ),
    rgba(36, 91, 209, 1)`,
    btTextColor: '#0d54ff',
  },
  TNS: {
    themeName: 'TNS',
    primaryColor: '#007AC2',
    secondaryColor: '#95C11F',
    logo: TNSLogo,
    logoMob: TNSLogoMob,
    ...defaultThemeColors,
    ...defaultFavicons,
  },
  bolt: {
    themeName: 'bolt',
    primaryColor: '#0096FF',
    secondaryColor: '#A06EE2',
    darkColor: '#262626',
    lightColor: '#C4C4C4',
    greenColor: '#11B175',
    redColor: '#F84428',
    blueColor: '#0D63CB',
    orangeColor: '#FF9B00',
    tealColor: '#00C9E7',
    purpleColor: '#7426D3',
    yellowColor: '#F4E704',
    logo: boltLogo,
    logoMob: boltLogo,
    typography: {
      fontFamily: [
        'Poppins, sans-serif',
      ],
    },
    landingGradient: 'linear-gradient(180deg, rgba(255, 255, 255, 0) 77.08%, #FFFFFF 100%), linear-gradient(90deg, rgba(242, 155, 252, 0.1) 0%, rgba(86, 116, 209, 0.1) 100%)',
    faviconLight: faviconboltLight,
    faviconDark: faviconboltLight,
  },
  eb: {
    themeName: 'nextgen',
    primaryColor: '#2D2A81',
    secondaryColor: '#F3C73C',
    logo: nextgenLogo,
    logoMob: nextgenLogo,
    ...defaultThemeColors,
    ...defaultFavicons,
    darkColor: '#1C1C28',
    lightColor: '#BCBDCD',
    greenColor: '#359B70',
    redColor: '#DA5454',
    blueColor: '#004FC4',
    orangeColor: '#F28100',
    tealColor: '#00B7C4',
    purpleColor: '#4C0099',
    coralColor: '#FF9EB7',
    yellowColor: '#FFDF00',
    landingGradient: 'linear-gradient(102.42deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%, #FFFFFF 100%)',
  },
};

export default themeConfig;
