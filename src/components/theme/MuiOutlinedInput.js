import styles from '../styles/variables.module.scss';
import getBrandColors from '../getBrandColors';

const MuiOutlinedInputTheme = (currentPrimaryColor, themeName) => ({
  MuiOutlinedInput: {
    styleOverrides: {
      root: {
        fontSize: '14px',
        fontWeight: '400',
        borderColor: styles.inputBorderColor,
        backgroundColor: 'white!important',
        '&.MuiInputBase-adornedStart': {
          paddingLeft: '0px',
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
          borderColor: styles.inputBorderColor,
        },
        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
          borderWidth: '1px',
          borderColor: getBrandColors(currentPrimaryColor, themeName)[500],
        },
        '&.Mui-focused .MuiInputAdornment-root': {
          color: getBrandColors(currentPrimaryColor, themeName)[500],
        },
        '&.Mui-disabled .MuiOutlinedInput-input': {
          color: `${styles.darkColor200}!important`,
        },
        '&.Mui-disabled .MuiOutlinedInput-notchedOutline': {
          borderColor: styles.lightColor200,
        },
        '&.MuiOutlinedInput_large': {
          fontSize: '16px',
        },
        '&.MuiOutlinedInput_large .MuiOutlinedInput-input': {
          height: '50px',
        },
      },
      input: {
        position: 'relative',
        order: '1',
        height: '40px',
        width: 'calc(100% - 26px)',
        boxSizing: 'border-box',
        paddingTop: '0',
        paddingBottom: '0',
        color: `${styles.darkColor500}!important`,
        '&:-internal-autofill-selected': {
          color: `${styles.inputTextColor}!important`,
        },
      },
      inputAdornedStart: {
        paddingLeft: '8px',
      },
    },
  },
  MuiInputAdornment: {
    styleOverrides: {
      root: {
        position: 'relative',
        left: '10px',
        color: styles.darkColor300,
      },
    },
  },
  MuiInputLabel: {
    styleOverrides: {
      outlined: {
        color: styles.darkColor200,
        transform: 'translate(40px, 12px) scale(1)',
        '&.MuiInputLabel-shrink': {
          transform: 'translate(14px, -6px) scale(0.85) !important',
          color: styles.darkColor300,
        },
        '&[for="combo-box-demo"]': {
          transform: 'translate(12px, 12px) scale(1)',
        },
      },
      root: {
        '&.Mui-focused': {
          color: styles.darkColor300,
        },
        '&.MuiInputLabel_large': {
          fontSize: '16px',
          transform: 'translate(15px, 16px) scale(1)',
        },
        '&.Mui-disabled': {
          color: `${styles.darkColor200}!important`,
        },
      },
    },
  },
  MuiFormControlLabel: {
    styleOverrides: {
      label: {
        fontSize: '14px',
      },
    },
  },
  MuiFormLabel: {
    styleOverrides: {
      root: {
        fontSize: '14px',
      },
    },
  },
});

export default MuiOutlinedInputTheme;
